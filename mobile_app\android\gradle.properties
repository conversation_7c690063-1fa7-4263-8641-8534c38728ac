org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.java.home=C:\\Program Files\\Android\\Android Studio\\jbr
android.useAndroidX=true
android.enableJetifier=true

# Gradle configuration
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# Android configuration
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Disable NDK completely
android.useDeprecatedNdk=false

# Kotlin configuration
kotlin.incremental=false
kotlin.incremental.android=false
kotlin.incremental.java=false
