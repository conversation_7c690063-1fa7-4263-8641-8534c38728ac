Metadata-Version: 2.4
Name: trustchain-auth
Version: 1.0.0
Summary: TrustChain-Auth: Behavioral biometrics authentication system
Author: TrustChain Team
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: fastapi==0.115.13
Requires-Dist: uvicorn[standard]==0.34.3
Requires-Dist: pydantic==2.11.7
Requires-Dist: pydantic-settings==2.10.0
Requires-Dist: pydantic[email]
Requires-Dist: email-validator>=2.0.0
Requires-Dist: sqlalchemy==2.0.23
Requires-Dist: alembic==1.13.1
Requires-Dist: psycopg2-binary==2.9.9
Requires-Dist: asyncpg==0.29.0
Requires-Dist: aiosqlite==0.19.0
Requires-Dist: python-jose[cryptography]==3.3.0
Requires-Dist: passlib[bcrypt]==1.7.4
Requires-Dist: python-multipart==0.0.6
Requires-Dist: bcrypt==4.1.2
Requires-Dist: cryptography==41.0.7
Requires-Dist: httpx==0.25.2
Requires-Dist: aiohttp==3.9.1
Requires-Dist: prometheus-client==0.22.1
Requires-Dist: structlog==25.4.0
Requires-Dist: sentry-sdk[fastapi]==2.30.0
Requires-Dist: python-json-logger==3.3.0
Requires-Dist: python-dotenv==1.1.0
Requires-Dist: pyyaml==6.0.2
Requires-Dist: tensorflow==2.14.0
Requires-Dist: scikit-learn==1.3.2
Requires-Dist: numpy==1.24.3
Requires-Dist: pandas==2.1.1
Provides-Extra: test
Requires-Dist: pytest==7.4.3; extra == "test"
Requires-Dist: pytest-asyncio==0.21.1; extra == "test"
Requires-Dist: pytest-cov==4.1.0; extra == "test"
Requires-Dist: aiosqlite==0.19.0; extra == "test"
Requires-Dist: httpx==0.25.2; extra == "test"
Requires-Dist: propcache==0.3.2; extra == "test"
Requires-Dist: coverage==7.9.1; extra == "test"
Provides-Extra: dev
Requires-Dist: black; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: isort; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: pre-commit; extra == "dev"
Dynamic: license-file

# TrustChain-Auth

🔐 Objective:
With growing threats like session hijacking, credential theft, and account takeovers, static authentication (passwords, PINs, biometrics) is no longer enough. PersonaLock aims to deliver a passwordless, continuous authentication system that verifies identity through behavioral biometrics — ensuring real-time fraud detection, zero user friction, and maximum privacy.

By learning how a user naturally interacts with their device (typing, swiping, navigating), PersonaLock builds a dynamic digital fingerprint that silently protects sessions, detects intrusions, and adapts over time.

⚙ Implementation:
We are building a Flutter-based mobile banking prototype powered by on-device machine learning to ensure real-time performance and full privacy. The app continuously tracks:

Typing dynamics (speed, rhythm, dwell time)

Touch behavior (swipe velocity, tap force, gesture sequence)

App navigation flow (screen paths, interaction timing)

Geolocation and contextual cues (time, location changes)

Our ML stack uses Autoencoders, One-Class SVMs, and Contrastive Learning models — trained in Python and deployed with TensorFlow Lite to ensure low-latency, private inference.

Additionally, we’ve integrated Smart Pop-Up Challenges that trigger during moderate anomalies — asking privacy-safe, non-repetitive questions to verify identity without being intrusive.

🔑 Key Features:
✅ Passwordless Behavior-Based Login

✅ Continuous Real-Time Behavior Monitoring

✅ Smart Pop-Up Verification Questions (adaptive, privacy-safe)

✅ On-Device ML Inference with TFLite

✅ Adaptive Risk Scoring Engine

✅ Session Auto-Lock / Silent Logout on High Risk

✅ Panic Gestures (shake or volume long-press) for duress signaling

✅ Privacy Dashboard (view/manage/delete behavior data)

🌐 Applications:
Fintech & Banking: Continuous behavioral auth for high-risk transactions

E-commerce: Passive fraud detection during checkout sessions

Digital Identity: Seamless and persistent identity validation

Accessibility Use Cases: Personalized profiles for elderly and differently-abled users

🎯 Final Result:
Our working prototype demonstrates a secure, seamless, and invisible background authentication system that adapts to user behavior in real-time. It prevents unauthorized access even after login, minimizes false positives, and provides clear feedback when challenges arise.

Users can view and control their data through a built-in privacy dashboard, while silent panic gestures offer emergency protection under duress.

🔮 Post-Campaign Plans:
Federated Learning: Enable crowd-sourced learning without sharing raw data

Explainable AI (XAI): Inform users with feedback like “unusual typing rhythm”

Threat Modeling Enhancements: Detect session hijacking, social engineering

Multi-Device Support: Sync behavior profiles across user devices securely

Pilot Studies: Collaborate with banks like Canara to validate in real-world banking contexts

🧰 Tech Stack:
ML Training: Python, TensorFlow, scikit-learn, Keras

Mobile App: Flutter + Dart

Model Deployment: TensorFlow Lite (on-device)

Sensor Access: Flutter plugins (touch, location, device info)

Backend (Optional): Firebase / FastAPI for alerts & logging (no personal data storage)

Versioning: GitHub
