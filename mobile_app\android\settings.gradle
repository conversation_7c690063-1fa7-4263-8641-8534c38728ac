pluginManagement {
    def localProperties = new Properties()
    def localPropertiesFile = new File(settingsDir, "local.properties")
    if (localPropertiesFile.exists()) {
        localPropertiesFile.withReader('UTF-8') { reader ->
            localProperties.load(reader)
        }
    }

    def flutterSdkPath = localProperties.getProperty('flutter.sdk')
    assert flutterSdkPath != null, "Flutter SDK not found. Define location with flutter.sdk in the local.properties file."

    includeBuild("${flutterSdkPath}/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        google()
        mavenCentral()
    }
}

include ':app'
